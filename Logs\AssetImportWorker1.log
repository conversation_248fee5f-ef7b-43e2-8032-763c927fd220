Using pre-set license
Built from '2021.3/staging' branch; Version is '2021.3.45f1 (0da89fac8e79) revision 895135'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2021.3.45f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
-logFile
Logs/AssetImportWorker1.log
-srvPort
50205
Successfully changed project path to: D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Unable to join player connection multicast group (err: 10022).
Unable to join player connection alternative multicast group (err: 10022).
[Physics::Module] Initialized MultithreadedJobDispatcher with {0} workers.
Refreshing native plugins compatible for Editor in 107.45 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.45f1 (0da89fac8e79)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56256
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.012525 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 498 ms
Refreshing native plugins compatible for Editor in 83.61 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.593 seconds
Domain Reload Profiling:
	ReloadAssembly (1594ms)
		BeginReloadAssembly (163ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (1263ms)
			LoadAssemblies (160ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (172ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (51ms)
			SetupLoadedEditorAssemblies (972ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (614ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (84ms)
				BeforeProcessingInitializeOnLoad (2ms)
				ProcessInitializeOnLoadAttributes (193ms)
				ProcessInitializeOnLoadMethodAttributes (79ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.016593 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.41 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.418 seconds
Domain Reload Profiling:
	ReloadAssembly (2419ms)
		BeginReloadAssembly (218ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (10ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (34ms)
		EndReloadAssembly (2006ms)
			LoadAssemblies (155ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (376ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (91ms)
			SetupLoadedEditorAssemblies (1355ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (81ms)
				ProcessInitializeOnLoadAttributes (1199ms)
				ProcessInitializeOnLoadMethodAttributes (35ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.11 seconds
Refreshing native plugins compatible for Editor in 2.29 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2305 Unused Serialized files (Serialized files now loaded: 0)
Unloading 172 unused Assets / (0.7 MB). Loaded Objects now: 2718.
Memory consumption went from 122.5 MB to 121.8 MB.
Total: 6.710100 ms (FindLiveObjects: 0.290500 ms CreateObjectMapping: 0.157600 ms MarkObjects: 5.505100 ms  DeleteObjects: 0.756100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 4570.094075 seconds.
  path: Assets/TRACTOR1.fbx
  artifactKey: Guid(c9413532aa8f1c544ac8e5e48f759c91) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/TRACTOR1.fbx using Guid(c9413532aa8f1c544ac8e5e48f759c91) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '417cd491c4cfb92b3a424590f07a2a6e') in 0.570600 seconds 
Number of asset objects unloaded after import = 145
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015997 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.32 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.286 seconds
Domain Reload Profiling:
	ReloadAssembly (2287ms)
		BeginReloadAssembly (241ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (55ms)
		EndReloadAssembly (1873ms)
			LoadAssemblies (165ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (367ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (70ms)
			SetupLoadedEditorAssemblies (1238ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (74ms)
				ProcessInitializeOnLoadAttributes (1089ms)
				ProcessInitializeOnLoadMethodAttributes (37ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (11ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.55 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2744.
Memory consumption went from 124.0 MB to 123.3 MB.
Total: 6.567000 ms (FindLiveObjects: 0.297800 ms CreateObjectMapping: 0.154100 ms MarkObjects: 5.417300 ms  DeleteObjects: 0.696700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016557 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.47 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.213 seconds
Domain Reload Profiling:
	ReloadAssembly (2214ms)
		BeginReloadAssembly (250ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (57ms)
		EndReloadAssembly (1803ms)
			LoadAssemblies (167ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (348ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (77ms)
			SetupLoadedEditorAssemblies (1193ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (73ms)
				ProcessInitializeOnLoadAttributes (1050ms)
				ProcessInitializeOnLoadMethodAttributes (30ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.32 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2759.
Memory consumption went from 124.0 MB to 123.3 MB.
Total: 8.733200 ms (FindLiveObjects: 0.394200 ms CreateObjectMapping: 0.171800 ms MarkObjects: 6.872500 ms  DeleteObjects: 1.292000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016065 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.39 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.218 seconds
Domain Reload Profiling:
	ReloadAssembly (2219ms)
		BeginReloadAssembly (263ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (67ms)
		EndReloadAssembly (1795ms)
			LoadAssemblies (163ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (321ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (70ms)
			SetupLoadedEditorAssemblies (1214ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (28ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (77ms)
				ProcessInitializeOnLoadAttributes (1064ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.87 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2774.
Memory consumption went from 124.0 MB to 123.3 MB.
Total: 8.586600 ms (FindLiveObjects: 0.342700 ms CreateObjectMapping: 0.166700 ms MarkObjects: 7.088500 ms  DeleteObjects: 0.987300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016435 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.34 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.143 seconds
Domain Reload Profiling:
	ReloadAssembly (2145ms)
		BeginReloadAssembly (257ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (68ms)
		EndReloadAssembly (1718ms)
			LoadAssemblies (161ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (296ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (64ms)
			SetupLoadedEditorAssemblies (1181ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (67ms)
				ProcessInitializeOnLoadAttributes (1041ms)
				ProcessInitializeOnLoadMethodAttributes (35ms)
				AfterProcessingInitializeOnLoad (14ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.39 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2789.
Memory consumption went from 124.0 MB to 123.3 MB.
Total: 7.812100 ms (FindLiveObjects: 0.697600 ms CreateObjectMapping: 0.168700 ms MarkObjects: 5.816200 ms  DeleteObjects: 1.127700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015959 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.39 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.348 seconds
Domain Reload Profiling:
	ReloadAssembly (2349ms)
		BeginReloadAssembly (254ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (10ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (54ms)
		EndReloadAssembly (1911ms)
			LoadAssemblies (170ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (337ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (110ms)
			SetupLoadedEditorAssemblies (1283ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (73ms)
				ProcessInitializeOnLoadAttributes (1139ms)
				ProcessInitializeOnLoadMethodAttributes (32ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.66 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2804.
Memory consumption went from 124.1 MB to 123.4 MB.
Total: 7.431500 ms (FindLiveObjects: 0.387400 ms CreateObjectMapping: 0.157800 ms MarkObjects: 6.155900 ms  DeleteObjects: 0.729300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014305 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.43 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.203 seconds
Domain Reload Profiling:
	ReloadAssembly (2204ms)
		BeginReloadAssembly (245ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (62ms)
		EndReloadAssembly (1789ms)
			LoadAssemblies (162ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (321ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (90ms)
			SetupLoadedEditorAssemblies (1186ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (74ms)
				ProcessInitializeOnLoadAttributes (1039ms)
				ProcessInitializeOnLoadMethodAttributes (34ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.99 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2819.
Memory consumption went from 124.1 MB to 123.4 MB.
Total: 7.265500 ms (FindLiveObjects: 0.359500 ms CreateObjectMapping: 0.159900 ms MarkObjects: 5.974300 ms  DeleteObjects: 0.770300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014487 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.44 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.224 seconds
Domain Reload Profiling:
	ReloadAssembly (2225ms)
		BeginReloadAssembly (277ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (59ms)
		EndReloadAssembly (1772ms)
			LoadAssemblies (180ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (324ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (76ms)
			SetupLoadedEditorAssemblies (1176ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (75ms)
				ProcessInitializeOnLoadAttributes (1031ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.44 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2834.
Memory consumption went from 124.1 MB to 123.4 MB.
Total: 7.347300 ms (FindLiveObjects: 0.370000 ms CreateObjectMapping: 0.173600 ms MarkObjects: 5.943400 ms  DeleteObjects: 0.858600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014876 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.32 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.312 seconds
Domain Reload Profiling:
	ReloadAssembly (2313ms)
		BeginReloadAssembly (271ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (50ms)
		EndReloadAssembly (1847ms)
			LoadAssemblies (182ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (416ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (87ms)
			SetupLoadedEditorAssemblies (1165ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (68ms)
				ProcessInitializeOnLoadAttributes (1028ms)
				ProcessInitializeOnLoadMethodAttributes (32ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.86 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2849.
Memory consumption went from 124.1 MB to 123.4 MB.
Total: 7.794100 ms (FindLiveObjects: 0.328800 ms CreateObjectMapping: 0.156200 ms MarkObjects: 6.454900 ms  DeleteObjects: 0.852800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015852 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.39 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.183 seconds
Domain Reload Profiling:
	ReloadAssembly (2184ms)
		BeginReloadAssembly (231ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (51ms)
		EndReloadAssembly (1798ms)
			LoadAssemblies (158ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (361ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (80ms)
			SetupLoadedEditorAssemblies (1175ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (71ms)
				ProcessInitializeOnLoadAttributes (1034ms)
				ProcessInitializeOnLoadMethodAttributes (32ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.07 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2864.
Memory consumption went from 124.1 MB to 123.4 MB.
Total: 8.616100 ms (FindLiveObjects: 0.649900 ms CreateObjectMapping: 0.355200 ms MarkObjects: 6.600700 ms  DeleteObjects: 1.008900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016610 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.55 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.221 seconds
Domain Reload Profiling:
	ReloadAssembly (2222ms)
		BeginReloadAssembly (266ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (60ms)
		EndReloadAssembly (1774ms)
			LoadAssemblies (167ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (333ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (87ms)
			SetupLoadedEditorAssemblies (1170ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (69ms)
				ProcessInitializeOnLoadAttributes (1029ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.83 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2879.
Memory consumption went from 124.2 MB to 123.5 MB.
Total: 6.918800 ms (FindLiveObjects: 0.338600 ms CreateObjectMapping: 0.157900 ms MarkObjects: 5.651000 ms  DeleteObjects: 0.770200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015510 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.49 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.218 seconds
Domain Reload Profiling:
	ReloadAssembly (2219ms)
		BeginReloadAssembly (248ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (62ms)
		EndReloadAssembly (1815ms)
			LoadAssemblies (155ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (324ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (86ms)
			SetupLoadedEditorAssemblies (1210ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (27ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (85ms)
				ProcessInitializeOnLoadAttributes (1053ms)
				ProcessInitializeOnLoadMethodAttributes (29ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.68 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2894.
Memory consumption went from 124.2 MB to 123.5 MB.
Total: 7.612900 ms (FindLiveObjects: 0.434300 ms CreateObjectMapping: 0.182000 ms MarkObjects: 6.173400 ms  DeleteObjects: 0.822100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015746 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.43 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.351 seconds
Domain Reload Profiling:
	ReloadAssembly (2352ms)
		BeginReloadAssembly (267ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (57ms)
		EndReloadAssembly (1892ms)
			LoadAssemblies (183ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (375ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (87ms)
			SetupLoadedEditorAssemblies (1242ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (73ms)
				ProcessInitializeOnLoadAttributes (1097ms)
				ProcessInitializeOnLoadMethodAttributes (32ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.69 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2909.
Memory consumption went from 124.2 MB to 123.5 MB.
Total: 6.930300 ms (FindLiveObjects: 0.398600 ms CreateObjectMapping: 0.174000 ms MarkObjects: 5.536500 ms  DeleteObjects: 0.820100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015856 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.30 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.195 seconds
Domain Reload Profiling:
	ReloadAssembly (2196ms)
		BeginReloadAssembly (241ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (49ms)
		EndReloadAssembly (1788ms)
			LoadAssemblies (164ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (347ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (82ms)
			SetupLoadedEditorAssemblies (1165ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (26ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (69ms)
				ProcessInitializeOnLoadAttributes (1025ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.91 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2924.
Memory consumption went from 124.2 MB to 123.5 MB.
Total: 7.027000 ms (FindLiveObjects: 0.494200 ms CreateObjectMapping: 0.203500 ms MarkObjects: 5.388100 ms  DeleteObjects: 0.939900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015960 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.45 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.262 seconds
Domain Reload Profiling:
	ReloadAssembly (2263ms)
		BeginReloadAssembly (284ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (70ms)
		EndReloadAssembly (1788ms)
			LoadAssemblies (180ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (332ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (77ms)
			SetupLoadedEditorAssemblies (1201ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (73ms)
				ProcessInitializeOnLoadAttributes (1057ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.88 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2939.
Memory consumption went from 124.2 MB to 123.5 MB.
Total: 6.972600 ms (FindLiveObjects: 0.416200 ms CreateObjectMapping: 0.160600 ms MarkObjects: 5.618100 ms  DeleteObjects: 0.776500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 4100.169076 seconds.
  path: Assets/RealisticCarControllerV3/Models/City/Materials/None__RoadTexture3_jpg.mat
  artifactKey: Guid(deccf23b84be2984a87b181504c70ed7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/RealisticCarControllerV3/Models/City/Materials/None__RoadTexture3_jpg.mat using Guid(deccf23b84be2984a87b181504c70ed7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3cde99923011f8c3a8e853e02a4ad1ec') in 0.636146 seconds 
Number of asset objects unloaded after import = 3
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015834 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.26 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.188 seconds
Domain Reload Profiling:
	ReloadAssembly (2189ms)
		BeginReloadAssembly (247ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (67ms)
		EndReloadAssembly (1785ms)
			LoadAssemblies (158ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (326ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (77ms)
			SetupLoadedEditorAssemblies (1201ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (22ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (70ms)
				ProcessInitializeOnLoadAttributes (1057ms)
				ProcessInitializeOnLoadMethodAttributes (35ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.04 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 2987.
Memory consumption went from 124.4 MB to 123.7 MB.
Total: 8.154100 ms (FindLiveObjects: 0.348400 ms CreateObjectMapping: 0.169100 ms MarkObjects: 6.778900 ms  DeleteObjects: 0.855900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015489 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.53 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.208 seconds
Domain Reload Profiling:
	ReloadAssembly (2209ms)
		BeginReloadAssembly (230ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (49ms)
		EndReloadAssembly (1809ms)
			LoadAssemblies (160ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (313ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (79ms)
			SetupLoadedEditorAssemblies (1230ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (39ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (83ms)
				ProcessInitializeOnLoadAttributes (1060ms)
				ProcessInitializeOnLoadMethodAttributes (32ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.38 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3002.
Memory consumption went from 124.4 MB to 123.7 MB.
Total: 8.695900 ms (FindLiveObjects: 0.365600 ms CreateObjectMapping: 0.166900 ms MarkObjects: 7.250700 ms  DeleteObjects: 0.911200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015771 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.41 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.228 seconds
Domain Reload Profiling:
	ReloadAssembly (2229ms)
		BeginReloadAssembly (261ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (12ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (50ms)
		EndReloadAssembly (1797ms)
			LoadAssemblies (160ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (340ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (93ms)
			SetupLoadedEditorAssemblies (1187ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (74ms)
				ProcessInitializeOnLoadAttributes (1046ms)
				ProcessInitializeOnLoadMethodAttributes (30ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.36 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3017.
Memory consumption went from 124.4 MB to 123.8 MB.
Total: 6.557500 ms (FindLiveObjects: 0.344600 ms CreateObjectMapping: 0.163100 ms MarkObjects: 5.160100 ms  DeleteObjects: 0.888600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015899 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.26 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.186 seconds
Domain Reload Profiling:
	ReloadAssembly (2187ms)
		BeginReloadAssembly (256ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (68ms)
		EndReloadAssembly (1777ms)
			LoadAssemblies (162ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (313ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (86ms)
			SetupLoadedEditorAssemblies (1195ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (70ms)
				ProcessInitializeOnLoadAttributes (1057ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.47 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3032.
Memory consumption went from 124.5 MB to 123.8 MB.
Total: 6.612400 ms (FindLiveObjects: 0.337300 ms CreateObjectMapping: 0.158300 ms MarkObjects: 5.367400 ms  DeleteObjects: 0.748100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016198 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.46 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.292 seconds
Domain Reload Profiling:
	ReloadAssembly (2293ms)
		BeginReloadAssembly (275ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (67ms)
		EndReloadAssembly (1819ms)
			LoadAssemblies (169ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (333ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (90ms)
			SetupLoadedEditorAssemblies (1220ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (73ms)
				ProcessInitializeOnLoadAttributes (1075ms)
				ProcessInitializeOnLoadMethodAttributes (32ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.25 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3047.
Memory consumption went from 124.5 MB to 123.8 MB.
Total: 8.837500 ms (FindLiveObjects: 0.555900 ms CreateObjectMapping: 0.215500 ms MarkObjects: 7.016400 ms  DeleteObjects: 1.047500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014186 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.32 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.222 seconds
Domain Reload Profiling:
	ReloadAssembly (2223ms)
		BeginReloadAssembly (245ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (51ms)
		EndReloadAssembly (1819ms)
			LoadAssemblies (169ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (301ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (76ms)
			SetupLoadedEditorAssemblies (1262ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (72ms)
				ProcessInitializeOnLoadAttributes (1115ms)
				ProcessInitializeOnLoadMethodAttributes (35ms)
				AfterProcessingInitializeOnLoad (14ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.18 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3062.
Memory consumption went from 124.5 MB to 123.8 MB.
Total: 8.802300 ms (FindLiveObjects: 0.572100 ms CreateObjectMapping: 0.224100 ms MarkObjects: 7.031300 ms  DeleteObjects: 0.973300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016014 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.41 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.236 seconds
Domain Reload Profiling:
	ReloadAssembly (2237ms)
		BeginReloadAssembly (259ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (51ms)
		EndReloadAssembly (1796ms)
			LoadAssemblies (162ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (301ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (80ms)
			SetupLoadedEditorAssemblies (1219ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (30ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (82ms)
				ProcessInitializeOnLoadAttributes (1060ms)
				ProcessInitializeOnLoadMethodAttributes (33ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.19 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3077.
Memory consumption went from 124.5 MB to 123.8 MB.
Total: 5.667800 ms (FindLiveObjects: 0.339900 ms CreateObjectMapping: 0.160500 ms MarkObjects: 4.439300 ms  DeleteObjects: 0.727000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015005 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.34 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.240 seconds
Domain Reload Profiling:
	ReloadAssembly (2241ms)
		BeginReloadAssembly (272ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (63ms)
		EndReloadAssembly (1788ms)
			LoadAssemblies (176ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (337ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (76ms)
			SetupLoadedEditorAssemblies (1192ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (85ms)
				ProcessInitializeOnLoadAttributes (1037ms)
				ProcessInitializeOnLoadMethodAttributes (32ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 3.10 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3092.
Memory consumption went from 124.5 MB to 123.8 MB.
Total: 9.204100 ms (FindLiveObjects: 0.369400 ms CreateObjectMapping: 0.229100 ms MarkObjects: 7.444800 ms  DeleteObjects: 1.159000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014131 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.46 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.311 seconds
Domain Reload Profiling:
	ReloadAssembly (2312ms)
		BeginReloadAssembly (278ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (56ms)
		EndReloadAssembly (1847ms)
			LoadAssemblies (181ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (329ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (73ms)
			SetupLoadedEditorAssemblies (1244ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (27ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (90ms)
				ProcessInitializeOnLoadAttributes (1081ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.36 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3107.
Memory consumption went from 124.6 MB to 123.9 MB.
Total: 7.219400 ms (FindLiveObjects: 0.421900 ms CreateObjectMapping: 0.169600 ms MarkObjects: 5.852100 ms  DeleteObjects: 0.774500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015799 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.46 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.252 seconds
Domain Reload Profiling:
	ReloadAssembly (2253ms)
		BeginReloadAssembly (242ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (66ms)
		EndReloadAssembly (1847ms)
			LoadAssemblies (153ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (389ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (79ms)
			SetupLoadedEditorAssemblies (1184ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (27ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (80ms)
				ProcessInitializeOnLoadAttributes (1030ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.90 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3122.
Memory consumption went from 124.6 MB to 123.9 MB.
Total: 7.774400 ms (FindLiveObjects: 0.377400 ms CreateObjectMapping: 0.197900 ms MarkObjects: 6.337500 ms  DeleteObjects: 0.860200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015922 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.35 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.293 seconds
Domain Reload Profiling:
	ReloadAssembly (2294ms)
		BeginReloadAssembly (268ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (57ms)
		EndReloadAssembly (1847ms)
			LoadAssemblies (163ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (389ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (79ms)
			SetupLoadedEditorAssemblies (1200ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (32ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (70ms)
				ProcessInitializeOnLoadAttributes (1048ms)
				ProcessInitializeOnLoadMethodAttributes (34ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.81 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3137.
Memory consumption went from 124.6 MB to 123.9 MB.
Total: 6.944200 ms (FindLiveObjects: 0.368900 ms CreateObjectMapping: 0.161500 ms MarkObjects: 5.650700 ms  DeleteObjects: 0.761700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016127 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.34 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.199 seconds
Domain Reload Profiling:
	ReloadAssembly (2201ms)
		BeginReloadAssembly (242ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (55ms)
		EndReloadAssembly (1785ms)
			LoadAssemblies (161ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (336ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (78ms)
			SetupLoadedEditorAssemblies (1192ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (71ms)
				ProcessInitializeOnLoadAttributes (1051ms)
				ProcessInitializeOnLoadMethodAttributes (32ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.68 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3152.
Memory consumption went from 124.6 MB to 123.9 MB.
Total: 7.147300 ms (FindLiveObjects: 0.392400 ms CreateObjectMapping: 0.281300 ms MarkObjects: 5.730200 ms  DeleteObjects: 0.742200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015827 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.48 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.168 seconds
Domain Reload Profiling:
	ReloadAssembly (2169ms)
		BeginReloadAssembly (228ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (56ms)
		EndReloadAssembly (1785ms)
			LoadAssemblies (146ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (311ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (78ms)
			SetupLoadedEditorAssemblies (1219ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (91ms)
				ProcessInitializeOnLoadAttributes (1058ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.38 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3167.
Memory consumption went from 124.6 MB to 123.9 MB.
Total: 7.578600 ms (FindLiveObjects: 0.421000 ms CreateObjectMapping: 0.176800 ms MarkObjects: 5.911900 ms  DeleteObjects: 1.067600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014484 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.41 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.229 seconds
Domain Reload Profiling:
	ReloadAssembly (2230ms)
		BeginReloadAssembly (238ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (61ms)
		EndReloadAssembly (1833ms)
			LoadAssemblies (149ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (322ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (77ms)
			SetupLoadedEditorAssemblies (1257ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (70ms)
				ProcessInitializeOnLoadAttributes (1112ms)
				ProcessInitializeOnLoadMethodAttributes (34ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.08 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3182.
Memory consumption went from 124.7 MB to 124.0 MB.
Total: 8.539000 ms (FindLiveObjects: 0.534100 ms CreateObjectMapping: 0.193800 ms MarkObjects: 6.827800 ms  DeleteObjects: 0.981700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015923 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.47 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.310 seconds
Domain Reload Profiling:
	ReloadAssembly (2311ms)
		BeginReloadAssembly (250ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (51ms)
		EndReloadAssembly (1886ms)
			LoadAssemblies (166ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (322ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (91ms)
			SetupLoadedEditorAssemblies (1278ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (30ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (86ms)
				ProcessInitializeOnLoadAttributes (1116ms)
				ProcessInitializeOnLoadMethodAttributes (32ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.08 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3197.
Memory consumption went from 124.7 MB to 124.0 MB.
Total: 8.857500 ms (FindLiveObjects: 0.515000 ms CreateObjectMapping: 0.283000 ms MarkObjects: 7.298500 ms  DeleteObjects: 0.759300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014363 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.47 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.194 seconds
Domain Reload Profiling:
	ReloadAssembly (2195ms)
		BeginReloadAssembly (235ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (52ms)
		EndReloadAssembly (1805ms)
			LoadAssemblies (154ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (327ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (76ms)
			SetupLoadedEditorAssemblies (1224ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (26ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (72ms)
				ProcessInitializeOnLoadAttributes (1080ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (11ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.85 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3212.
Memory consumption went from 124.7 MB to 124.0 MB.
Total: 7.037100 ms (FindLiveObjects: 0.452000 ms CreateObjectMapping: 0.183400 ms MarkObjects: 5.620100 ms  DeleteObjects: 0.780500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015948 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.39 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.279 seconds
Domain Reload Profiling:
	ReloadAssembly (2280ms)
		BeginReloadAssembly (251ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (58ms)
		EndReloadAssembly (1857ms)
			LoadAssemblies (170ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (371ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (93ms)
			SetupLoadedEditorAssemblies (1207ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (72ms)
				ProcessInitializeOnLoadAttributes (1067ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.29 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3227.
Memory consumption went from 124.8 MB to 124.1 MB.
Total: 7.642600 ms (FindLiveObjects: 0.410500 ms CreateObjectMapping: 0.176600 ms MarkObjects: 6.211300 ms  DeleteObjects: 0.842900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015725 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.38 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.222 seconds
Domain Reload Profiling:
	ReloadAssembly (2223ms)
		BeginReloadAssembly (245ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (60ms)
		EndReloadAssembly (1799ms)
			LoadAssemblies (158ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (334ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (74ms)
			SetupLoadedEditorAssemblies (1213ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (2ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (72ms)
				ProcessInitializeOnLoadAttributes (1073ms)
				ProcessInitializeOnLoadMethodAttributes (29ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.74 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3242.
Memory consumption went from 124.7 MB to 124.0 MB.
Total: 6.153900 ms (FindLiveObjects: 0.497000 ms CreateObjectMapping: 0.171100 ms MarkObjects: 4.708400 ms  DeleteObjects: 0.776200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016068 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.35 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.193 seconds
Domain Reload Profiling:
	ReloadAssembly (2194ms)
		BeginReloadAssembly (253ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (65ms)
		EndReloadAssembly (1776ms)
			LoadAssemblies (157ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (327ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (81ms)
			SetupLoadedEditorAssemblies (1185ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (70ms)
				ProcessInitializeOnLoadAttributes (1037ms)
				ProcessInitializeOnLoadMethodAttributes (37ms)
				AfterProcessingInitializeOnLoad (15ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (11ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.61 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3257.
Memory consumption went from 124.8 MB to 124.1 MB.
Total: 7.860900 ms (FindLiveObjects: 0.454200 ms CreateObjectMapping: 0.268400 ms MarkObjects: 6.297700 ms  DeleteObjects: 0.839500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015888 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.41 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.237 seconds
Domain Reload Profiling:
	ReloadAssembly (2238ms)
		BeginReloadAssembly (240ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (62ms)
		EndReloadAssembly (1819ms)
			LoadAssemblies (148ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (314ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (91ms)
			SetupLoadedEditorAssemblies (1228ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (74ms)
				ProcessInitializeOnLoadAttributes (1079ms)
				ProcessInitializeOnLoadMethodAttributes (34ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.06 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3272.
Memory consumption went from 124.8 MB to 124.1 MB.
Total: 7.920900 ms (FindLiveObjects: 0.412300 ms CreateObjectMapping: 0.178200 ms MarkObjects: 6.469800 ms  DeleteObjects: 0.859600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015809 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.43 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.212 seconds
Domain Reload Profiling:
	ReloadAssembly (2213ms)
		BeginReloadAssembly (254ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (64ms)
		EndReloadAssembly (1804ms)
			LoadAssemblies (165ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (307ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (70ms)
			SetupLoadedEditorAssemblies (1231ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (85ms)
				ProcessInitializeOnLoadAttributes (1076ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (19ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.83 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3287.
Memory consumption went from 124.9 MB to 124.2 MB.
Total: 8.036200 ms (FindLiveObjects: 0.458300 ms CreateObjectMapping: 0.187300 ms MarkObjects: 6.280300 ms  DeleteObjects: 1.108900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016293 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.45 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.368 seconds
Domain Reload Profiling:
	ReloadAssembly (2369ms)
		BeginReloadAssembly (259ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (59ms)
		EndReloadAssembly (1930ms)
			LoadAssemblies (167ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (362ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (88ms)
			SetupLoadedEditorAssemblies (1298ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (2ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (73ms)
				ProcessInitializeOnLoadAttributes (1151ms)
				ProcessInitializeOnLoadMethodAttributes (33ms)
				AfterProcessingInitializeOnLoad (14ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.68 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3302.
Memory consumption went from 124.9 MB to 124.2 MB.
Total: 6.794700 ms (FindLiveObjects: 0.382900 ms CreateObjectMapping: 0.171300 ms MarkObjects: 5.473200 ms  DeleteObjects: 0.766100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014339 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.72 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.395 seconds
Domain Reload Profiling:
	ReloadAssembly (2397ms)
		BeginReloadAssembly (254ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (63ms)
		EndReloadAssembly (1962ms)
			LoadAssemblies (153ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (413ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (101ms)
			SetupLoadedEditorAssemblies (1264ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (3ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (91ms)
				ProcessInitializeOnLoadAttributes (1099ms)
				ProcessInitializeOnLoadMethodAttributes (32ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.21 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3317.
Memory consumption went from 124.8 MB to 124.1 MB.
Total: 7.710300 ms (FindLiveObjects: 0.383500 ms CreateObjectMapping: 0.178800 ms MarkObjects: 6.253700 ms  DeleteObjects: 0.892400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015949 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.40 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.153 seconds
Domain Reload Profiling:
	ReloadAssembly (2154ms)
		BeginReloadAssembly (236ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (57ms)
		EndReloadAssembly (1766ms)
			LoadAssemblies (152ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (299ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (79ms)
			SetupLoadedEditorAssemblies (1202ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (2ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (73ms)
				ProcessInitializeOnLoadAttributes (1055ms)
				ProcessInitializeOnLoadMethodAttributes (34ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.58 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3332.
Memory consumption went from 124.9 MB to 124.2 MB.
Total: 7.203600 ms (FindLiveObjects: 0.381700 ms CreateObjectMapping: 0.165600 ms MarkObjects: 5.829600 ms  DeleteObjects: 0.825500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016094 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.41 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.267 seconds
Domain Reload Profiling:
	ReloadAssembly (2269ms)
		BeginReloadAssembly (247ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (70ms)
		EndReloadAssembly (1867ms)
			LoadAssemblies (150ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (320ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (82ms)
			SetupLoadedEditorAssemblies (1270ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (40ms)
				SetLoadedEditorAssemblies (2ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (79ms)
				ProcessInitializeOnLoadAttributes (1105ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.66 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3347.
Memory consumption went from 124.9 MB to 124.2 MB.
Total: 7.888500 ms (FindLiveObjects: 0.435000 ms CreateObjectMapping: 0.192500 ms MarkObjects: 6.433000 ms  DeleteObjects: 0.826700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015794 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.54 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.327 seconds
Domain Reload Profiling:
	ReloadAssembly (2328ms)
		BeginReloadAssembly (263ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (57ms)
		EndReloadAssembly (1874ms)
			LoadAssemblies (174ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (313ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (95ms)
			SetupLoadedEditorAssemblies (1268ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (26ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (87ms)
				ProcessInitializeOnLoadAttributes (1106ms)
				ProcessInitializeOnLoadMethodAttributes (33ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.65 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3362.
Memory consumption went from 125.0 MB to 124.2 MB.
Total: 7.389300 ms (FindLiveObjects: 0.416300 ms CreateObjectMapping: 0.209400 ms MarkObjects: 5.850300 ms  DeleteObjects: 0.912100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016221 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.58 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.243 seconds
Domain Reload Profiling:
	ReloadAssembly (2244ms)
		BeginReloadAssembly (253ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (60ms)
		EndReloadAssembly (1819ms)
			LoadAssemblies (150ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (341ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (94ms)
			SetupLoadedEditorAssemblies (1189ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (26ms)
				SetLoadedEditorAssemblies (2ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (76ms)
				ProcessInitializeOnLoadAttributes (1040ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.99 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3377.
Memory consumption went from 125.0 MB to 124.3 MB.
Total: 8.071200 ms (FindLiveObjects: 0.452900 ms CreateObjectMapping: 0.222200 ms MarkObjects: 6.440900 ms  DeleteObjects: 0.953400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 16.57 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 56 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (0.7 MB). Loaded Objects now: 3377.
Memory consumption went from 78.6 MB to 77.9 MB.
Total: 9.528900 ms (FindLiveObjects: 0.443600 ms CreateObjectMapping: 0.191800 ms MarkObjects: 7.927700 ms  DeleteObjects: 0.964300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015862 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.46 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.282 seconds
Domain Reload Profiling:
	ReloadAssembly (2283ms)
		BeginReloadAssembly (252ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (48ms)
		EndReloadAssembly (1848ms)
			LoadAssemblies (159ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (321ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (113ms)
			SetupLoadedEditorAssemblies (1227ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (71ms)
				ProcessInitializeOnLoadAttributes (1088ms)
				ProcessInitializeOnLoadMethodAttributes (30ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.12 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3392.
Memory consumption went from 125.0 MB to 124.3 MB.
Total: 7.233800 ms (FindLiveObjects: 0.415900 ms CreateObjectMapping: 0.162800 ms MarkObjects: 5.807400 ms  DeleteObjects: 0.846300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 3695.249143 seconds.
  path: Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/project/ico_skull.png
  artifactKey: Guid(d563d6ce25cd2ca48b185ec47a74a3fb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/project/ico_skull.png using Guid(d563d6ce25cd2ca48b185ec47a74a3fb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '58e88302bc8656a5d7852181220f2217') in 0.153572 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/ico_skull.png
  artifactKey: Guid(01976ad42d31f494e8040d3e32e283dc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/ico_skull.png using Guid(01976ad42d31f494e8040d3e32e283dc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3d07a8c2f7af8c0f2d580c1ca7a519cd') in 0.047307 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/ico_skull_border.png
  artifactKey: Guid(6dfd924c0ba23844fb5d04ad1560de22) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/ico_skull_border.png using Guid(6dfd924c0ba23844fb5d04ad1560de22) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ce55a2d76c0978d3af6c41a7a92389ff') in 0.025140 seconds 
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.345572 seconds.
  path: Assets/RealisticCarControllerV3/Models/Materials/RCCSkybox_Day.mat
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Number of updated assets reloaded before import = 0
Start importing Assets/RealisticCarControllerV3/Models/Materials/RCCSkybox_Day.mat using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4249275128518b35c7495a576a2d942d') in 0.264523 seconds 
Number of asset objects unloaded after import = 1
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015918 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.39 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.295 seconds
Domain Reload Profiling:
	ReloadAssembly (2296ms)
		BeginReloadAssembly (275ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (66ms)
		EndReloadAssembly (1839ms)
			LoadAssemblies (179ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (366ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (98ms)
			SetupLoadedEditorAssemblies (1193ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (75ms)
				ProcessInitializeOnLoadAttributes (1051ms)
				ProcessInitializeOnLoadMethodAttributes (30ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.23 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3410.
Memory consumption went from 125.0 MB to 124.3 MB.
Total: 6.012900 ms (FindLiveObjects: 0.423600 ms CreateObjectMapping: 0.163900 ms MarkObjects: 4.654200 ms  DeleteObjects: 0.770100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015532 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.40 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.276 seconds
Domain Reload Profiling:
	ReloadAssembly (2277ms)
		BeginReloadAssembly (253ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (58ms)
		EndReloadAssembly (1857ms)
			LoadAssemblies (162ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (372ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (80ms)
			SetupLoadedEditorAssemblies (1227ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (2ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (72ms)
				ProcessInitializeOnLoadAttributes (1080ms)
				ProcessInitializeOnLoadMethodAttributes (35ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.04 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3425.
Memory consumption went from 125.0 MB to 124.3 MB.
Total: 6.859200 ms (FindLiveObjects: 0.363300 ms CreateObjectMapping: 0.157700 ms MarkObjects: 5.624100 ms  DeleteObjects: 0.713000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016214 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.47 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.202 seconds
Domain Reload Profiling:
	ReloadAssembly (2203ms)
		BeginReloadAssembly (260ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (15ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (52ms)
		EndReloadAssembly (1789ms)
			LoadAssemblies (157ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (309ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (83ms)
			SetupLoadedEditorAssemblies (1206ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (25ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (81ms)
				ProcessInitializeOnLoadAttributes (1054ms)
				ProcessInitializeOnLoadMethodAttributes (32ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.32 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3440.
Memory consumption went from 125.1 MB to 124.3 MB.
Total: 7.568700 ms (FindLiveObjects: 0.528200 ms CreateObjectMapping: 0.169600 ms MarkObjects: 6.105700 ms  DeleteObjects: 0.763500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015289 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.39 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.296 seconds
Domain Reload Profiling:
	ReloadAssembly (2298ms)
		BeginReloadAssembly (270ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (64ms)
		EndReloadAssembly (1859ms)
			LoadAssemblies (162ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (337ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (80ms)
			SetupLoadedEditorAssemblies (1252ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (93ms)
				ProcessInitializeOnLoadAttributes (1084ms)
				ProcessInitializeOnLoadMethodAttributes (35ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.65 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3455.
Memory consumption went from 125.1 MB to 124.4 MB.
Total: 6.913600 ms (FindLiveObjects: 0.551700 ms CreateObjectMapping: 0.205200 ms MarkObjects: 5.382800 ms  DeleteObjects: 0.772800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015714 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.35 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.275 seconds
Domain Reload Profiling:
	ReloadAssembly (2276ms)
		BeginReloadAssembly (291ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (10ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (69ms)
		EndReloadAssembly (1810ms)
			LoadAssemblies (177ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (350ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (87ms)
			SetupLoadedEditorAssemblies (1187ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (75ms)
				ProcessInitializeOnLoadAttributes (1043ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.87 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3470.
Memory consumption went from 125.1 MB to 124.4 MB.
Total: 7.309900 ms (FindLiveObjects: 0.410900 ms CreateObjectMapping: 0.161100 ms MarkObjects: 5.925200 ms  DeleteObjects: 0.811200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016321 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.264 seconds
Domain Reload Profiling:
	ReloadAssembly (2265ms)
		BeginReloadAssembly (246ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (49ms)
		EndReloadAssembly (1849ms)
			LoadAssemblies (159ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (311ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (78ms)
			SetupLoadedEditorAssemblies (1282ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (22ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (72ms)
				ProcessInitializeOnLoadAttributes (1138ms)
				ProcessInitializeOnLoadMethodAttributes (35ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.95 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3485.
Memory consumption went from 125.0 MB to 124.3 MB.
Total: 7.257800 ms (FindLiveObjects: 0.389700 ms CreateObjectMapping: 0.161400 ms MarkObjects: 5.927400 ms  DeleteObjects: 0.778300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015625 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.71 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.142 seconds
Domain Reload Profiling:
	ReloadAssembly (2143ms)
		BeginReloadAssembly (243ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (52ms)
		EndReloadAssembly (1745ms)
			LoadAssemblies (155ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (300ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (66ms)
			SetupLoadedEditorAssemblies (1205ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (81ms)
				ProcessInitializeOnLoadAttributes (1056ms)
				ProcessInitializeOnLoadMethodAttributes (30ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.96 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3500.
Memory consumption went from 125.1 MB to 124.4 MB.
Total: 8.820700 ms (FindLiveObjects: 0.680300 ms CreateObjectMapping: 0.200700 ms MarkObjects: 6.617100 ms  DeleteObjects: 1.320400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015833 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.33 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.227 seconds
Domain Reload Profiling:
	ReloadAssembly (2228ms)
		BeginReloadAssembly (232ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (62ms)
		EndReloadAssembly (1828ms)
			LoadAssemblies (141ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (337ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (78ms)
			SetupLoadedEditorAssemblies (1237ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (76ms)
				ProcessInitializeOnLoadAttributes (1094ms)
				ProcessInitializeOnLoadMethodAttributes (30ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.94 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3515.
Memory consumption went from 125.1 MB to 124.4 MB.
Total: 8.050800 ms (FindLiveObjects: 0.425800 ms CreateObjectMapping: 0.176500 ms MarkObjects: 6.588200 ms  DeleteObjects: 0.859000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014127 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.34 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.272 seconds
Domain Reload Profiling:
	ReloadAssembly (2273ms)
		BeginReloadAssembly (263ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (57ms)
		EndReloadAssembly (1835ms)
			LoadAssemblies (175ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (357ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (71ms)
			SetupLoadedEditorAssemblies (1226ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (26ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (78ms)
				ProcessInitializeOnLoadAttributes (1077ms)
				ProcessInitializeOnLoadMethodAttributes (30ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.49 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3530.
Memory consumption went from 125.2 MB to 124.5 MB.
Total: 6.672900 ms (FindLiveObjects: 0.387700 ms CreateObjectMapping: 0.158500 ms MarkObjects: 5.372000 ms  DeleteObjects: 0.753700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016344 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.73 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.231 seconds
Domain Reload Profiling:
	ReloadAssembly (2232ms)
		BeginReloadAssembly (249ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (48ms)
		EndReloadAssembly (1818ms)
			LoadAssemblies (157ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (297ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (73ms)
			SetupLoadedEditorAssemblies (1271ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (24ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (83ms)
				ProcessInitializeOnLoadAttributes (1119ms)
				ProcessInitializeOnLoadMethodAttributes (31ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.57 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3545.
Memory consumption went from 125.2 MB to 124.5 MB.
Total: 7.124300 ms (FindLiveObjects: 0.380100 ms CreateObjectMapping: 0.173700 ms MarkObjects: 5.763300 ms  DeleteObjects: 0.806100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016012 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.01 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.150 seconds
Domain Reload Profiling:
	ReloadAssembly (2150ms)
		BeginReloadAssembly (246ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (53ms)
		EndReloadAssembly (1744ms)
			LoadAssemblies (155ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (295ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (68ms)
			SetupLoadedEditorAssemblies (1192ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (29ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (75ms)
				ProcessInitializeOnLoadAttributes (1044ms)
				ProcessInitializeOnLoadMethodAttributes (29ms)
				AfterProcessingInitializeOnLoad (12ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 2.72 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3560.
Memory consumption went from 125.2 MB to 124.5 MB.
Total: 8.561000 ms (FindLiveObjects: 0.570200 ms CreateObjectMapping: 0.252700 ms MarkObjects: 6.776600 ms  DeleteObjects: 0.958700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015680 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 1.45 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Completed reload, in  2.291 seconds
Domain Reload Profiling:
	ReloadAssembly (2292ms)
		BeginReloadAssembly (256ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (66ms)
		EndReloadAssembly (1846ms)
			LoadAssemblies (158ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (334ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (74ms)
			SetupLoadedEditorAssemblies (1254ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (23ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (76ms)
				ProcessInitializeOnLoadAttributes (1103ms)
				ProcessInitializeOnLoadMethodAttributes (35ms)
				AfterProcessingInitializeOnLoad (13ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.58 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2273 Unused Serialized files (Serialized files now loaded: 0)
Unloading 155 unused Assets / (0.7 MB). Loaded Objects now: 3575.
Memory consumption went from 125.2 MB to 124.5 MB.
Total: 6.732000 ms (FindLiveObjects: 0.413000 ms CreateObjectMapping: 0.162800 ms MarkObjects: 5.415700 ms  DeleteObjects: 0.738800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:CustomObjectIndexerAttribute: bc11b3a6c3213fcdd17b65e7da85e133 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
